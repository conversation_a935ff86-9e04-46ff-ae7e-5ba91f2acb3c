#!/bin/bash

# GEMINI ASM Continuous Monitoring Script
# Part of the GEMINI ASM System v2.0
# Global External Monitoring & Intelligence Network Infrastructure
# WARNING: Only use with explicit authorization

# Configuration
CONFIG_FILE="${1:-asm_monitoring_config.yaml}"
GEMINI_VERSION="2.0"
MONITOR_DIR="GEMINI_Monitor_$(date +%Y%m%d_%H%M%S)"
LOG_FILE="/var/log/gemini_asm_monitor.log"

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

# Logging functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

log_section() {
    echo -e "\n${BLUE}=== $1 ===${NC}\n" | tee -a "$LOG_FILE"
}

# Authorization check for monitoring
validate_monitoring_authorization() {
    log_section "GEMINI ASM MONITORING AUTHORIZATION"
    
    if [[ ! -f "$CONFIG_FILE" ]]; then
        log_error "Configuration file not found: $CONFIG_FILE"
        exit 1
    fi
    
    # Extract target domains from config
    DOMAINS=$(grep -A 10 "additional_domains:" "$CONFIG_FILE" | grep -E "^\s*-" | sed 's/^\s*-\s*//' | tr -d '"')
    PRIMARY_DOMAIN=$(grep "primary_domain:" "$CONFIG_FILE" | cut -d'"' -f2)
    
    log_info "Primary domain: $PRIMARY_DOMAIN"
    log_info "Additional domains: $DOMAINS"
    
    echo "Do you have authorization for continuous monitoring of these domains? (yes/no)"
    read -r auth_response
    
    if [[ "$auth_response" != "yes" ]]; then
        log_error "Monitoring authorization not confirmed. Exiting."
        exit 1
    fi
    
    log_info "Monitoring authorization confirmed for GEMINI ASM v$GEMINI_VERSION"
}

# Setup monitoring environment
setup_monitoring() {
    log_section "GEMINI MONITORING ENVIRONMENT SETUP"
    
    mkdir -p "$MONITOR_DIR"/{baseline,current,alerts,reports}
    cd "$MONITOR_DIR" || exit 1
    
    log_info "Created monitoring directory: $MONITOR_DIR"
    
    # Create monitoring log
    {
        echo "=== GEMINI ASM CONTINUOUS MONITORING ==="
        echo "System: GEMINI ASM v$GEMINI_VERSION"
        echo "Monitoring Started: $(date)"
        echo "Configuration: $CONFIG_FILE"
        echo "Primary Domain: $PRIMARY_DOMAIN"
        echo "Operator: $(whoami)"
        echo "Host: $(hostname)"
        echo ""
    } > monitoring_log.txt
}

# Subdomain monitoring function
monitor_subdomains() {
    log_info "Starting subdomain monitoring for $PRIMARY_DOMAIN"
    
    # Current subdomain discovery
    subfinder -d "$PRIMARY_DOMAIN" -silent > current/subdomains_current.txt 2>/dev/null
    amass enum -d "$PRIMARY_DOMAIN" -passive > current/subdomains_amass.txt 2>/dev/null
    
    # Combine and deduplicate
    cat current/subdomains_*.txt 2>/dev/null | sort -u > current/all_subdomains.txt
    
    # Compare with baseline if exists
    if [[ -f baseline/all_subdomains.txt ]]; then
        # Find new subdomains
        comm -13 baseline/all_subdomains.txt current/all_subdomains.txt > alerts/new_subdomains.txt
        
        if [[ -s alerts/new_subdomains.txt ]]; then
            log_warning "New subdomains discovered:"
            cat alerts/new_subdomains.txt | while read -r subdomain; do
                log_warning "  - $subdomain"
            done
            
            # Generate alert
            {
                echo "GEMINI ASM ALERT: New Subdomains Discovered"
                echo "Timestamp: $(date)"
                echo "Domain: $PRIMARY_DOMAIN"
                echo "New subdomains:"
                cat alerts/new_subdomains.txt
            } > alerts/subdomain_alert_$(date +%Y%m%d_%H%M%S).txt
        else
            log_info "No new subdomains detected"
        fi
    else
        # First run - create baseline
        cp current/all_subdomains.txt baseline/
        log_info "Baseline created with $(wc -l < baseline/all_subdomains.txt) subdomains"
    fi
}

# Port monitoring function
monitor_ports() {
    log_info "Starting port monitoring"
    
    # Get unique IPs from current subdomains
    while read -r subdomain; do
        ip=$(dig +short "$subdomain" A | head -1)
        [[ -n "$ip" && "$ip" != *"NXDOMAIN"* ]] && echo "$ip"
    done < current/all_subdomains.txt | sort -u > current/unique_ips.txt
    
    # Quick port scan on common ports
    if command -v nmap &> /dev/null; then
        nmap -sS -T4 -p 80,443,22,21,25,53,110,143,993,995,8080,8443 \
            -iL current/unique_ips.txt --open > current/port_scan.txt 2>/dev/null
        
        # Extract open ports summary
        grep -E "open|filtered" current/port_scan.txt > current/open_ports.txt
        
        # Compare with baseline
        if [[ -f baseline/open_ports.txt ]]; then
            comm -13 baseline/open_ports.txt current/open_ports.txt > alerts/new_ports.txt
            
            if [[ -s alerts/new_ports.txt ]]; then
                log_warning "New open ports detected:"
                cat alerts/new_ports.txt
                
                # Generate alert
                {
                    echo "GEMINI ASM ALERT: New Open Ports Detected"
                    echo "Timestamp: $(date)"
                    echo "New ports:"
                    cat alerts/new_ports.txt
                } > alerts/port_alert_$(date +%Y%m%d_%H%M%S).txt
            fi
        else
            cp current/open_ports.txt baseline/
            log_info "Port baseline created"
        fi
    fi
}

# Certificate monitoring function
monitor_certificates() {
    log_info "Starting certificate monitoring"
    
    while read -r subdomain; do
        # Check SSL certificate
        if timeout 10 openssl s_client -connect "$subdomain:443" -servername "$subdomain" \
            </dev/null 2>/dev/null | openssl x509 -noout -dates > "current/cert_$subdomain.txt" 2>/dev/null; then
            
            # Extract expiry date
            expiry=$(grep "notAfter" "current/cert_$subdomain.txt" | cut -d'=' -f2)
            expiry_epoch=$(date -d "$expiry" +%s 2>/dev/null)
            current_epoch=$(date +%s)
            days_until_expiry=$(( (expiry_epoch - current_epoch) / 86400 ))
            
            if [[ $days_until_expiry -lt 30 ]]; then
                log_warning "Certificate expiring soon: $subdomain ($days_until_expiry days)"
                
                # Generate alert
                {
                    echo "GEMINI ASM ALERT: Certificate Expiring Soon"
                    echo "Timestamp: $(date)"
                    echo "Subdomain: $subdomain"
                    echo "Days until expiry: $days_until_expiry"
                    echo "Expiry date: $expiry"
                } > alerts/cert_expiry_${subdomain}_$(date +%Y%m%d).txt
            fi
        fi
    done < current/all_subdomains.txt
}

# Vulnerability monitoring function
monitor_vulnerabilities() {
    log_info "Starting vulnerability monitoring"
    
    # Quick vulnerability check using nuclei if available
    if command -v nuclei &> /dev/null; then
        nuclei -l current/all_subdomains.txt -severity critical,high \
            -silent -json > current/vulnerabilities.json 2>/dev/null
        
        if [[ -s current/vulnerabilities.json ]]; then
            vuln_count=$(wc -l < current/vulnerabilities.json)
            log_warning "Found $vuln_count high/critical vulnerabilities"
            
            # Generate alert
            {
                echo "GEMINI ASM ALERT: High/Critical Vulnerabilities Detected"
                echo "Timestamp: $(date)"
                echo "Vulnerability count: $vuln_count"
                echo "Details:"
                jq -r '.info.name + " - " + .host' current/vulnerabilities.json 2>/dev/null
            } > alerts/vulnerability_alert_$(date +%Y%m%d_%H%M%S).txt
        fi
    fi
}

# Generate monitoring report
generate_monitoring_report() {
    log_section "GENERATING MONITORING REPORT"
    
    {
        echo "=== GEMINI ASM MONITORING REPORT ==="
        echo "Generated: $(date)"
        echo "System: GEMINI ASM v$GEMINI_VERSION"
        echo "Primary Domain: $PRIMARY_DOMAIN"
        echo ""
        echo "=== SUMMARY ==="
        echo "Total subdomains monitored: $(wc -l < current/all_subdomains.txt 2>/dev/null || echo "0")"
        echo "Unique IP addresses: $(wc -l < current/unique_ips.txt 2>/dev/null || echo "0")"
        echo "New subdomains found: $(wc -l < alerts/new_subdomains.txt 2>/dev/null || echo "0")"
        echo "New ports detected: $(wc -l < alerts/new_ports.txt 2>/dev/null || echo "0")"
        echo "Certificate alerts: $(ls alerts/cert_expiry_*.txt 2>/dev/null | wc -l)"
        echo "Vulnerability alerts: $(ls alerts/vulnerability_alert_*.txt 2>/dev/null | wc -l)"
        echo ""
        echo "=== RECOMMENDATIONS ==="
        echo "1. Review all new assets for business necessity"
        echo "2. Investigate new open ports for security implications"
        echo "3. Renew expiring certificates before expiration"
        echo "4. Address high/critical vulnerabilities immediately"
        echo "5. Update asset inventory and security controls"
        echo ""
        echo "=== NEXT MONITORING CYCLE ==="
        echo "Recommended: 24 hours for subdomains, 7 days for ports"
        echo "Certificate monitoring: Daily"
        echo "Vulnerability scanning: Every 3 days"
    } > reports/monitoring_report_$(date +%Y%m%d_%H%M%S).txt
    
    log_info "Monitoring report generated: reports/monitoring_report_$(date +%Y%m%d_%H%M%S).txt"
}

# Send alerts function (placeholder for integration)
send_alerts() {
    if [[ -n "$(ls alerts/*.txt 2>/dev/null)" ]]; then
        log_info "Alerts generated - integrate with notification system"
        # Integration points for SIEM, email, Slack, etc.
        # This would be customized based on the monitoring configuration
    fi
}

# Main monitoring function
main() {
    echo -e "${PURPLE}🔍 GEMINI ASM Continuous Monitoring v$GEMINI_VERSION${NC}"
    echo "Global External Monitoring & Intelligence Network Infrastructure"
    echo ""
    
    if [[ -z "$CONFIG_FILE" || ! -f "$CONFIG_FILE" ]]; then
        log_error "Usage: $0 [config_file]"
        log_error "Default: $0 asm_monitoring_config.yaml"
        exit 1
    fi
    
    validate_monitoring_authorization
    setup_monitoring
    monitor_subdomains
    monitor_ports
    monitor_certificates
    monitor_vulnerabilities
    generate_monitoring_report
    send_alerts
    
    log_info "🎯 GEMINI ASM monitoring cycle completed"
    log_info "📁 Results directory: $MONITOR_DIR"
    log_info "📊 Latest report: reports/monitoring_report_$(date +%Y%m%d_%H%M%S).txt"
    
    echo ""
    echo -e "${GREEN}✅ GEMINI ASM Monitoring Complete${NC}"
    echo "Schedule next monitoring cycle according to your requirements."
}

# Execute main function
main "$@"
