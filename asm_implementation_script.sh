#!/bin/bash

# GEMINI Attack Surface Management Implementation Script
# Part of the GEMINI ASM System v2.0
# WARNING: Only use with explicit authorization for target domain
# This script implements the comprehensive GEMINI ASM methodology

# Configuration
TARGET_DOMAIN="$1"
OUTPUT_DIR="GEMINI_ASM_Assessment_$(date +%Y%m%d_%H%M%S)"
TOOLS_DIR="/opt/security-tools"
GEMINI_VERSION="2.0"

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_section() {
    echo -e "\n${BLUE}=== $1 ===${NC}\n"
}

# GEMINI Authorization validation function
validate_authorization() {
    echo -e "${RED}🚨 GEMINI ASM AUTHORIZATION CHECK 🚨${NC}"
    echo "Target domain: $TARGET_DOMAIN"
    echo "GEMINI ASM System v$GEMINI_VERSION"
    echo ""
    echo "⚠️  MANDATORY COMPLIANCE CHECKPOINT ⚠️"
    echo "This system is designed EXCLUSIVELY for authorized security testing."
    echo ""
    echo "Do you have explicit written authorization to test this domain? (yes/no)"
    read -r authorization

    if [[ "$authorization" != "yes" ]]; then
        print_error "Authorization not confirmed. Exiting per GEMINI compliance requirements."
        exit 1
    fi

    echo "Please provide authorization reference/ticket number:"
    read -r auth_ref
    echo "Please provide authorized scope (e.g., external assets only):"
    read -r auth_scope

    # Create comprehensive authorization record
    {
        echo "=== GEMINI ASM AUTHORIZATION RECORD ==="
        echo "System Version: GEMINI ASM v$GEMINI_VERSION"
        echo "Authorization Reference: $auth_ref"
        echo "Authorized Scope: $auth_scope"
        echo "Target Domain: $TARGET_DOMAIN"
        echo "Operator: $(whoami)"
        echo "Host System: $(hostname)"
        echo "Timestamp: $(date)"
        echo "IP Address: $(curl -s ifconfig.me 2>/dev/null || echo "Unable to determine")"
        echo ""
        echo "LEGAL COMPLIANCE:"
        echo "- Written authorization obtained: YES"
        echo "- Scope clearly defined: YES"
        echo "- Qualified personnel: YES"
        echo "- Responsible disclosure protocols: ACTIVE"
    } > "$OUTPUT_DIR/00_authorization_and_scope.txt"
}

# GEMINI Environment setup function
setup_environment() {
    print_section "GEMINI ASM ENVIRONMENT SETUP"

    # Create output directory structure
    mkdir -p "$OUTPUT_DIR"
    cd "$OUTPUT_DIR" || exit 1

    # Create subdirectories for organized output
    mkdir -p raw_data/{nmap_scans,vulnerability_reports,ssl_certificates,tool_outputs}
    mkdir -p evidence/{screenshots,proof_of_concept,network_diagrams}
    mkdir -p configs/{tool_configurations,compliance_mappings}

    print_status "Created GEMINI ASM directory structure: $OUTPUT_DIR"

    # Create comprehensive assessment log
    {
        echo "=== GEMINI ASM ASSESSMENT LOG ==="
        echo "System: GEMINI ASM v$GEMINI_VERSION"
        echo "Assessment Started: $(date)"
        echo "Target Domain: $TARGET_DOMAIN"
        echo "Operator: $(whoami)"
        echo "Host System: $(hostname)"
        echo "Working Directory: $(pwd)"
        echo "Assessment ID: $(basename "$OUTPUT_DIR")"
        echo ""
        echo "METHODOLOGY: 6-Phase GEMINI ASM Framework"
        echo "Phase 1: Reconnaissance & Asset Discovery"
        echo "Phase 2: Web Application & API Analysis"
        echo "Phase 3: Vulnerability Assessment & Exploitation Analysis"
        echo "Phase 4: Threat Intelligence & Risk Correlation"
        echo "Phase 5: Continuous Monitoring Framework"
        echo "Phase 6: Strategic Reporting & Remediation"
        echo ""
    } > assessment_log.txt

    # Copy monitoring configuration template
    if [[ -f "../asm_monitoring_config.yaml" ]]; then
        cp "../asm_monitoring_config.yaml" configs/
        print_status "Copied monitoring configuration template"
    fi
}

# Phase 1: Asset Discovery
phase1_asset_discovery() {
    print_section "PHASE 1: ASSET DISCOVERY & ENUMERATION"
    
    # 1.1 Domain & Subdomain Discovery
    print_status "Starting subdomain enumeration..."
    
    # Subfinder
    if command -v subfinder &> /dev/null; then
        print_status "Running subfinder..."
        subfinder -d "$TARGET_DOMAIN" -o subdomains_subfinder.txt 2>&1 | tee -a 01_domain_discovery.txt
    fi
    
    # Amass
    if command -v amass &> /dev/null; then
        print_status "Running amass enum..."
        amass enum -d "$TARGET_DOMAIN" -o subdomains_amass.txt 2>&1 | tee -a 01_domain_discovery.txt
    fi
    
    # Certificate Transparency
    print_status "Checking certificate transparency logs..."
    curl -s "https://crt.sh/?q=%25.$TARGET_DOMAIN&output=json" | \
        jq -r '.[].name_value' | sed 's/\*\.//g' | sort -u > subdomains_crt.txt 2>&1 | tee -a 01_domain_discovery.txt
    
    # Combine and deduplicate subdomains
    cat subdomains_*.txt 2>/dev/null | sort -u > all_subdomains.txt
    
    # DNS resolution
    print_status "Resolving subdomains to IP addresses..."
    while read -r subdomain; do
        ip=$(dig +short "$subdomain" A | head -1)
        if [[ -n "$ip" && "$ip" != *"NXDOMAIN"* ]]; then
            echo "$subdomain,$ip" >> resolved_subdomains.csv
        fi
    done < all_subdomains.txt
    
    # 1.2 Network Infrastructure Discovery
    print_status "Discovering network infrastructure..."
    
    # WHOIS lookup
    whois "$TARGET_DOMAIN" > whois_domain.txt 2>&1
    
    # ASN enumeration
    if command -v asnlookup &> /dev/null; then
        asnlookup -d "$TARGET_DOMAIN" > asn_info.txt 2>&1
    fi
    
    # Consolidate Phase 1 results
    {
        echo "=== PHASE 1: ASSET DISCOVERY RESULTS ==="
        echo "Timestamp: $(date)"
        echo ""
        echo "Total subdomains found: $(wc -l < all_subdomains.txt)"
        echo "Resolved subdomains: $(wc -l < resolved_subdomains.csv)"
        echo ""
        echo "=== SUBDOMAIN LIST ==="
        cat all_subdomains.txt
        echo ""
        echo "=== RESOLVED IPs ==="
        cat resolved_subdomains.csv
    } > 01_domain_discovery.txt
}

# Phase 2: Service Discovery
phase2_service_discovery() {
    print_section "PHASE 2: SERVICE & PORT DISCOVERY"
    
    print_status "Starting port scanning..."
    
    # Extract unique IPs
    cut -d',' -f2 resolved_subdomains.csv | sort -u > unique_ips.txt
    
    # Nmap scan
    if command -v nmap &> /dev/null; then
        print_status "Running nmap scan..."
        nmap -sS -sV -O -A -T4 -iL unique_ips.txt -oA nmap_scan 2>&1 | tee 03_service_discovery.txt
        
        # Parse nmap results
        if [[ -f nmap_scan.xml ]]; then
            print_status "Parsing nmap results..."
            # Extract open ports and services
            grep -E "open|filtered" nmap_scan.gnmap | \
                awk '{print $2 " - " $4}' >> open_ports_summary.txt
        fi
    fi
    
    # HTTP/HTTPS service enumeration
    print_status "Enumerating web services..."
    while IFS=',' read -r subdomain ip; do
        # Check HTTP
        if curl -s -I "http://$subdomain" --max-time 10 | head -1 | grep -q "200\|301\|302"; then
            echo "http://$subdomain - Active" >> web_services.txt
        fi
        
        # Check HTTPS
        if curl -s -I "https://$subdomain" --max-time 10 | head -1 | grep -q "200\|301\|302"; then
            echo "https://$subdomain - Active" >> web_services.txt
        fi
    done < resolved_subdomains.csv
}

# Phase 3: Web Application Discovery
phase3_webapp_discovery() {
    print_section "PHASE 3: WEB APPLICATION DISCOVERY"
    
    print_status "Analyzing web applications..."
    
    # Technology detection
    if command -v whatweb &> /dev/null; then
        print_status "Running whatweb technology detection..."
        whatweb -i web_services.txt --log-brief=whatweb_results.txt 2>&1 | tee -a 04_webapp_discovery.txt
    fi
    
    # Directory enumeration (limited scope)
    if command -v gobuster &> /dev/null; then
        print_status "Running directory enumeration (common paths only)..."
        while read -r url; do
            if [[ "$url" == *"Active"* ]]; then
                target_url=$(echo "$url" | cut -d' ' -f1)
                print_status "Scanning: $target_url"
                gobuster dir -u "$target_url" -w /usr/share/wordlists/dirb/common.txt \
                    -t 10 -q --timeout 10s >> directory_enum.txt 2>&1
            fi
        done < web_services.txt
    fi
    
    # SSL/TLS analysis
    print_status "Analyzing SSL/TLS configurations..."
    while IFS=',' read -r subdomain ip; do
        if openssl s_client -connect "$subdomain:443" -servername "$subdomain" \
            </dev/null 2>/dev/null | openssl x509 -noout -text > "ssl_$subdomain.txt" 2>/dev/null; then
            echo "$subdomain - SSL Certificate analyzed" >> ssl_analysis.txt
        fi
    done < resolved_subdomains.csv
}

# Phase 4: Vulnerability Assessment
phase4_vulnerability_assessment() {
    print_section "PHASE 4: VULNERABILITY ASSESSMENT"
    
    print_status "Starting vulnerability assessment..."
    
    # Nikto web vulnerability scanner
    if command -v nikto &> /dev/null; then
        print_status "Running Nikto web vulnerability scan..."
        while read -r url; do
            if [[ "$url" == *"Active"* ]]; then
                target_url=$(echo "$url" | cut -d' ' -f1)
                nikto -h "$target_url" -Format txt -output "nikto_$(echo "$target_url" | sed 's|https\?://||' | tr '/' '_').txt"
            fi
        done < web_services.txt
    fi
    
    # SSL/TLS vulnerability check
    if command -v testssl.sh &> /dev/null; then
        print_status "Running SSL/TLS vulnerability assessment..."
        while IFS=',' read -r subdomain ip; do
            testssl.sh --quiet --jsonfile "testssl_$subdomain.json" "$subdomain:443" 2>/dev/null
        done < resolved_subdomains.csv
    fi
    
    # Consolidate vulnerability findings
    {
        echo "=== VULNERABILITY ASSESSMENT RESULTS ==="
        echo "Timestamp: $(date)"
        echo ""
        echo "=== WEB VULNERABILITIES ==="
        find . -name "nikto_*.txt" -exec echo "File: {}" \; -exec cat {} \; -exec echo "" \;
        echo ""
        echo "=== SSL/TLS ISSUES ==="
        find . -name "testssl_*.json" -exec jq -r '.vulnerabilities[]? | select(.severity=="HIGH" or .severity=="CRITICAL") | .id + ": " + .finding' {} \; 2>/dev/null
    } > 06_vulnerability_scan.txt
}

# Phase 5: Risk Analysis
phase5_risk_analysis() {
    print_section "PHASE 5: RISK ANALYSIS & PRIORITIZATION"
    
    print_status "Analyzing and prioritizing risks..."
    
    {
        echo "=== RISK ANALYSIS REPORT ==="
        echo "Assessment Date: $(date)"
        echo "Target: $TARGET_DOMAIN"
        echo ""
        echo "=== ATTACK SURFACE SUMMARY ==="
        echo "Total Subdomains: $(wc -l < all_subdomains.txt)"
        echo "Active Web Services: $(wc -l < web_services.txt)"
        echo "Open Ports: $(wc -l < open_ports_summary.txt 2>/dev/null || echo "0")"
        echo ""
        echo "=== HIGH-RISK FINDINGS ==="
        echo "Review vulnerability scan results for critical and high-severity issues"
        echo ""
        echo "=== RECOMMENDATIONS ==="
        echo "1. Review and remediate identified vulnerabilities"
        echo "2. Implement proper SSL/TLS configurations"
        echo "3. Remove or secure unnecessary exposed services"
        echo "4. Implement security headers and best practices"
        echo "5. Regular monitoring and reassessment"
    } > 10_risk_analysis.txt
}

# Generate final report
generate_report() {
    print_section "GENERATING FINAL REPORT"
    
    {
        echo "ATTACK SURFACE MANAGEMENT ASSESSMENT REPORT"
        echo "============================================="
        echo ""
        echo "Target: $TARGET_DOMAIN"
        echo "Assessment Date: $(date)"
        echo "Operator: $(whoami)"
        echo ""
        echo "EXECUTIVE SUMMARY"
        echo "=================="
        echo "This assessment identified the external attack surface for $TARGET_DOMAIN"
        echo "including subdomains, services, and potential security vulnerabilities."
        echo ""
        echo "KEY FINDINGS:"
        echo "- Subdomains discovered: $(wc -l < all_subdomains.txt)"
        echo "- Active web services: $(wc -l < web_services.txt)"
        echo "- Security issues identified: See detailed vulnerability report"
        echo ""
        echo "RECOMMENDATIONS:"
        echo "1. Review all identified assets for business necessity"
        echo "2. Implement security controls for exposed services"
        echo "3. Address identified vulnerabilities by priority"
        echo "4. Establish continuous monitoring processes"
        echo ""
        echo "DETAILED FINDINGS:"
        echo "=================="
        echo "See individual phase reports for technical details:"
        echo "- 01_domain_discovery.txt"
        echo "- 03_service_discovery.txt"
        echo "- 04_webapp_discovery.txt"
        echo "- 06_vulnerability_scan.txt"
        echo "- 10_risk_analysis.txt"
    } > 11_executive_report.txt
    
    print_status "Assessment complete. Results saved in: $OUTPUT_DIR"
}

# GEMINI Main execution function
main() {
    if [[ -z "$TARGET_DOMAIN" ]]; then
        echo -e "${RED}GEMINI ASM System v$GEMINI_VERSION${NC}"
        print_error "Usage: $0 <target_domain>"
        print_error "Example: $0 example.com"
        echo ""
        echo "GEMINI ASM - Global External Monitoring & Intelligence Network Infrastructure"
        echo "Comprehensive Attack Surface Management for authorized security testing"
        exit 1
    fi

    echo -e "${BLUE}🚀 GEMINI ASM System v$GEMINI_VERSION${NC}"
    echo "Global External Monitoring & Intelligence Network Infrastructure"
    echo ""

    validate_authorization
    setup_environment
    phase1_asset_discovery
    phase2_service_discovery
    phase3_webapp_discovery
    phase4_vulnerability_assessment
    phase5_risk_analysis
    generate_report

    print_status "🎯 GEMINI ASM assessment completed successfully!"
    print_status "📁 Results directory: $OUTPUT_DIR"
    print_status "📊 Review executive report: 10_executive_report.txt"
    print_status "🔧 Technical details: 11_technical_report.txt"

    echo ""
    echo -e "${GREEN}✅ GEMINI ASM Assessment Complete${NC}"
    echo "Remember to follow responsible disclosure practices for any findings."
}

# Execute main function
main "$@"
