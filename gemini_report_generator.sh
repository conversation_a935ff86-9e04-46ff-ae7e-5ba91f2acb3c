#!/bin/bash

# GEMINI ASM Report Generator
# Part of the GEMINI ASM System v2.0
# Global External Monitoring & Intelligence Network Infrastructure

# Configuration
ASSESSMENT_DIR="$1"
TEMPLATE_FILE="${2:-asm_risk_assessment_template.md}"
GEMINI_VERSION="2.0"

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

# Utility functions
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_section() {
    echo -e "\n${BLUE}=== $1 ===${NC}\n"
}

# Validate inputs
validate_inputs() {
    if [[ -z "$ASSESSMENT_DIR" || ! -d "$ASSESSMENT_DIR" ]]; then
        print_error "Usage: $0 <assessment_directory> [template_file]"
        print_error "Example: $0 GEMINI_ASM_Assessment_20240101_1200"
        exit 1
    fi
    
    if [[ ! -f "$TEMPLATE_FILE" ]]; then
        print_error "Template file not found: $TEMPLATE_FILE"
        exit 1
    fi
    
    print_status "Generating reports for: $ASSESSMENT_DIR"
    print_status "Using template: $TEMPLATE_FILE"
}

# Extract assessment metadata
extract_metadata() {
    cd "$ASSESSMENT_DIR" || exit 1
    
    # Extract basic information
    TARGET_DOMAIN=$(grep "Target Domain:" assessment_log.txt | cut -d':' -f2 | xargs)
    ASSESSMENT_DATE=$(grep "Assessment Started:" assessment_log.txt | cut -d':' -f2- | xargs)
    OPERATOR=$(grep "Operator:" assessment_log.txt | cut -d':' -f2 | xargs)
    
    # Count findings
    TOTAL_SUBDOMAINS=$(wc -l < 01_domain_discovery.txt 2>/dev/null | grep -o '[0-9]*' || echo "0")
    TOTAL_SERVICES=$(grep -c "Active" 03_webapp_discovery.txt 2>/dev/null || echo "0")
    
    print_status "Target: $TARGET_DOMAIN"
    print_status "Assessment Date: $ASSESSMENT_DATE"
    print_status "Subdomains: $TOTAL_SUBDOMAINS"
    print_status "Active Services: $TOTAL_SERVICES"
}

# Analyze vulnerabilities and generate risk scores
analyze_vulnerabilities() {
    print_section "VULNERABILITY ANALYSIS"
    
    # Initialize counters
    CRITICAL_COUNT=0
    HIGH_COUNT=0
    MEDIUM_COUNT=0
    LOW_COUNT=0
    INFO_COUNT=0
    
    # Analyze vulnerability scan results
    if [[ -f "05_vulnerability_scan.txt" ]]; then
        CRITICAL_COUNT=$(grep -i "critical\|9\.[0-9]\|10\.0" 05_vulnerability_scan.txt | wc -l)
        HIGH_COUNT=$(grep -i "high\|[7-8]\.[0-9]" 05_vulnerability_scan.txt | wc -l)
        MEDIUM_COUNT=$(grep -i "medium\|[4-6]\.[0-9]" 05_vulnerability_scan.txt | wc -l)
        LOW_COUNT=$(grep -i "low\|[1-3]\.[0-9]" 05_vulnerability_scan.txt | wc -l)
        INFO_COUNT=$(grep -i "info\|0\.[0-9]" 05_vulnerability_scan.txt | wc -l)
    fi
    
    TOTAL_VULNS=$((CRITICAL_COUNT + HIGH_COUNT + MEDIUM_COUNT + LOW_COUNT + INFO_COUNT))
    
    print_status "Critical: $CRITICAL_COUNT"
    print_status "High: $HIGH_COUNT"
    print_status "Medium: $MEDIUM_COUNT"
    print_status "Low: $LOW_COUNT"
    print_status "Info: $INFO_COUNT"
    print_status "Total: $TOTAL_VULNS"
}

# Generate executive summary
generate_executive_summary() {
    print_section "GENERATING EXECUTIVE SUMMARY"
    
    # Calculate risk percentages
    if [[ $TOTAL_VULNS -gt 0 ]]; then
        CRITICAL_PCT=$(( (CRITICAL_COUNT * 100) / TOTAL_VULNS ))
        HIGH_PCT=$(( (HIGH_COUNT * 100) / TOTAL_VULNS ))
        MEDIUM_PCT=$(( (MEDIUM_COUNT * 100) / TOTAL_VULNS ))
        LOW_PCT=$(( (LOW_COUNT * 100) / TOTAL_VULNS ))
        INFO_PCT=$(( (INFO_COUNT * 100) / TOTAL_VULNS ))
    else
        CRITICAL_PCT=0; HIGH_PCT=0; MEDIUM_PCT=0; LOW_PCT=0; INFO_PCT=0
    fi
    
    # Determine overall risk level
    if [[ $CRITICAL_COUNT -gt 0 ]]; then
        OVERALL_RISK="Critical"
    elif [[ $HIGH_COUNT -gt 0 ]]; then
        OVERALL_RISK="High"
    elif [[ $MEDIUM_COUNT -gt 0 ]]; then
        OVERALL_RISK="Medium"
    else
        OVERALL_RISK="Low"
    fi
    
    # Generate executive summary
    {
        echo "# GEMINI ASM Executive Summary Report"
        echo ""
        echo "**Generated by:** GEMINI ASM System v$GEMINI_VERSION"
        echo "**Assessment Date:** $ASSESSMENT_DATE"
        echo "**Target Organization:** $TARGET_DOMAIN"
        echo "**Assessor:** $OPERATOR"
        echo "**Overall Risk Level:** $OVERALL_RISK"
        echo ""
        echo "## Key Findings Overview"
        echo ""
        echo "| Risk Level | Count | Percentage |"
        echo "|------------|-------|------------|"
        echo "| Critical   | $CRITICAL_COUNT   | ${CRITICAL_PCT}%      |"
        echo "| High       | $HIGH_COUNT   | ${HIGH_PCT}%      |"
        echo "| Medium     | $MEDIUM_COUNT   | ${MEDIUM_PCT}%      |"
        echo "| Low        | $LOW_COUNT   | ${LOW_PCT}%      |"
        echo "| Info       | $INFO_COUNT   | ${INFO_PCT}%      |"
        echo ""
        echo "## Attack Surface Statistics"
        echo ""
        echo "- **Total Subdomains Discovered:** $TOTAL_SUBDOMAINS"
        echo "- **Active Web Services:** $TOTAL_SERVICES"
        echo "- **Vulnerabilities Identified:** $TOTAL_VULNS"
        echo "- **Assessment Methodology:** GEMINI 6-Phase Framework"
        echo ""
        echo "## Business Impact Assessment"
        echo ""
        if [[ $CRITICAL_COUNT -gt 0 ]]; then
            echo "- **Immediate Risk:** High - Critical vulnerabilities require immediate attention"
        elif [[ $HIGH_COUNT -gt 0 ]]; then
            echo "- **Immediate Risk:** Medium - High-priority vulnerabilities identified"
        else
            echo "- **Immediate Risk:** Low - No critical vulnerabilities identified"
        fi
        echo "- **Data Exposure Risk:** $([ $CRITICAL_COUNT -gt 0 ] && echo "High" || echo "Medium")"
        echo "- **Compliance Impact:** $([ $((CRITICAL_COUNT + HIGH_COUNT)) -gt 0 ] && echo "High" || echo "Low")"
        echo "- **Reputation Risk:** $([ $CRITICAL_COUNT -gt 0 ] && echo "High" || echo "Medium")"
        echo ""
        echo "## Immediate Actions Required"
        echo ""
        if [[ $CRITICAL_COUNT -gt 0 ]]; then
            echo "1. **URGENT:** Address $CRITICAL_COUNT critical vulnerabilities within 24 hours"
        fi
        if [[ $HIGH_COUNT -gt 0 ]]; then
            echo "2. **HIGH PRIORITY:** Remediate $HIGH_COUNT high-severity issues within 48 hours"
        fi
        echo "3. **MONITORING:** Implement continuous attack surface monitoring"
        echo "4. **VALIDATION:** Conduct remediation validation testing"
        echo ""
        echo "## Strategic Recommendations"
        echo ""
        echo "1. **Asset Management:** Implement comprehensive asset inventory and classification"
        echo "2. **Vulnerability Management:** Establish regular vulnerability assessment cycles"
        echo "3. **Security Controls:** Deploy defense-in-depth security architecture"
        echo "4. **Incident Response:** Enhance security incident response capabilities"
        echo "5. **Compliance:** Align security controls with regulatory requirements"
        echo ""
        echo "---"
        echo "*This report was generated by GEMINI ASM System v$GEMINI_VERSION*"
        echo "*For technical details, refer to the comprehensive technical report*"
    } > GEMINI_Executive_Summary.md
    
    print_status "Executive summary generated: GEMINI_Executive_Summary.md"
}

# Generate technical report
generate_technical_report() {
    print_section "GENERATING TECHNICAL REPORT"
    
    {
        echo "# GEMINI ASM Technical Assessment Report"
        echo ""
        echo "**System:** GEMINI ASM v$GEMINI_VERSION"
        echo "**Assessment Date:** $ASSESSMENT_DATE"
        echo "**Target:** $TARGET_DOMAIN"
        echo "**Methodology:** 6-Phase GEMINI ASM Framework"
        echo ""
        echo "## Assessment Methodology"
        echo ""
        echo "This assessment followed the GEMINI ASM 6-Phase Framework:"
        echo ""
        echo "1. **Phase 1:** Reconnaissance & Asset Discovery"
        echo "2. **Phase 2:** Web Application & API Analysis"
        echo "3. **Phase 3:** Vulnerability Assessment & Exploitation Analysis"
        echo "4. **Phase 4:** Threat Intelligence & Risk Correlation"
        echo "5. **Phase 5:** Continuous Monitoring Framework"
        echo "6. **Phase 6:** Strategic Reporting & Remediation"
        echo ""
        echo "## Phase 1: Asset Discovery Results"
        echo ""
        if [[ -f "01_domain_discovery.txt" ]]; then
            echo "### Discovered Assets"
            echo "\`\`\`"
            head -20 01_domain_discovery.txt
            echo "\`\`\`"
            echo ""
        fi
        
        echo "## Phase 2: Web Application Analysis"
        echo ""
        if [[ -f "03_webapp_discovery.txt" ]]; then
            echo "### Web Technologies Identified"
            echo "\`\`\`"
            head -20 03_webapp_discovery.txt
            echo "\`\`\`"
            echo ""
        fi
        
        echo "## Phase 3: Vulnerability Assessment"
        echo ""
        if [[ -f "05_vulnerability_scan.txt" ]]; then
            echo "### Security Vulnerabilities"
            echo "\`\`\`"
            head -30 05_vulnerability_scan.txt
            echo "\`\`\`"
            echo ""
        fi
        
        echo "## Risk Analysis Summary"
        echo ""
        echo "- **Critical Vulnerabilities:** $CRITICAL_COUNT"
        echo "- **High-Risk Issues:** $HIGH_COUNT"
        echo "- **Medium-Risk Issues:** $MEDIUM_COUNT"
        echo "- **Low-Risk Issues:** $LOW_COUNT"
        echo "- **Informational Items:** $INFO_COUNT"
        echo ""
        echo "## Remediation Priorities"
        echo ""
        echo "### Immediate Actions (0-24 hours)"
        if [[ $CRITICAL_COUNT -gt 0 ]]; then
            echo "- Address all critical vulnerabilities"
            echo "- Implement emergency security controls"
            echo "- Notify security incident response team"
        else
            echo "- No immediate critical actions required"
        fi
        echo ""
        echo "### Short-term Actions (1-7 days)"
        if [[ $HIGH_COUNT -gt 0 ]]; then
            echo "- Remediate high-severity vulnerabilities"
            echo "- Review and update security configurations"
            echo "- Implement additional monitoring controls"
        fi
        echo ""
        echo "### Medium-term Actions (1-4 weeks)"
        if [[ $MEDIUM_COUNT -gt 0 ]]; then
            echo "- Address medium-risk vulnerabilities"
            echo "- Enhance security architecture"
            echo "- Conduct security awareness training"
        fi
        echo ""
        echo "## Continuous Monitoring Recommendations"
        echo ""
        echo "1. **Daily:** Subdomain and certificate monitoring"
        echo "2. **Weekly:** Port and service change detection"
        echo "3. **Monthly:** Comprehensive vulnerability assessment"
        echo "4. **Quarterly:** Full attack surface review"
        echo ""
        echo "## Tool Outputs and Evidence"
        echo ""
        echo "Raw tool outputs and evidence files are available in:"
        echo "- \`raw_data/\` - Tool outputs and scan results"
        echo "- \`evidence/\` - Screenshots and proof-of-concept materials"
        echo "- \`configs/\` - Configuration files and compliance mappings"
        echo ""
        echo "---"
        echo "*Generated by GEMINI ASM System v$GEMINI_VERSION*"
        echo "*Global External Monitoring & Intelligence Network Infrastructure*"
    } > GEMINI_Technical_Report.md
    
    print_status "Technical report generated: GEMINI_Technical_Report.md"
}

# Generate compliance report
generate_compliance_report() {
    print_section "GENERATING COMPLIANCE REPORT"
    
    {
        echo "# GEMINI ASM Compliance Assessment Report"
        echo ""
        echo "**Framework Alignment:** NIST CSF, ISO 27001, PCI DSS, GDPR"
        echo "**Assessment Date:** $ASSESSMENT_DATE"
        echo "**Target:** $TARGET_DOMAIN"
        echo ""
        echo "## Compliance Framework Mapping"
        echo ""
        echo "### NIST Cybersecurity Framework"
        echo "- **Identify (ID):** Asset inventory and risk assessment completed"
        echo "- **Protect (PR):** Security control gaps identified"
        echo "- **Detect (DE):** Monitoring framework recommendations provided"
        echo "- **Respond (RS):** Incident response considerations included"
        echo "- **Recover (RC):** Business continuity implications assessed"
        echo ""
        echo "### ISO 27001 Controls"
        echo "- **A.12.6.1:** Management of technical vulnerabilities - $([ $TOTAL_VULNS -gt 0 ] && echo "Non-compliant" || echo "Compliant")"
        echo "- **A.13.1.1:** Network controls - Assessment completed"
        echo "- **A.14.2.1:** Secure development policy - Web application security assessed"
        echo ""
        echo "### Regulatory Considerations"
        if [[ $CRITICAL_COUNT -gt 0 ]]; then
            echo "- **High Risk:** Critical vulnerabilities may impact regulatory compliance"
            echo "- **Action Required:** Immediate remediation to maintain compliance posture"
        else
            echo "- **Moderate Risk:** No critical compliance violations identified"
            echo "- **Recommendation:** Continue regular assessment cycles"
        fi
        echo ""
        echo "## Audit Trail"
        echo ""
        echo "- **Authorization:** Documented in 00_authorization_and_scope.txt"
        echo "- **Methodology:** GEMINI 6-Phase Framework applied"
        echo "- **Evidence:** Comprehensive tool outputs preserved"
        echo "- **Chain of Custody:** Maintained throughout assessment"
        echo ""
        echo "---"
        echo "*GEMINI ASM System v$GEMINI_VERSION - Compliance Assessment*"
    } > GEMINI_Compliance_Report.md
    
    print_status "Compliance report generated: GEMINI_Compliance_Report.md"
}

# Main function
main() {
    echo -e "${PURPLE}📊 GEMINI ASM Report Generator v$GEMINI_VERSION${NC}"
    echo "Global External Monitoring & Intelligence Network Infrastructure"
    echo ""
    
    validate_inputs
    extract_metadata
    analyze_vulnerabilities
    generate_executive_summary
    generate_technical_report
    generate_compliance_report
    
    print_section "REPORT GENERATION COMPLETE"
    print_status "📁 Assessment Directory: $ASSESSMENT_DIR"
    print_status "📋 Executive Summary: GEMINI_Executive_Summary.md"
    print_status "🔧 Technical Report: GEMINI_Technical_Report.md"
    print_status "⚖️ Compliance Report: GEMINI_Compliance_Report.md"
    
    echo ""
    echo -e "${GREEN}✅ GEMINI ASM Reports Generated Successfully${NC}"
    echo "Reports are ready for stakeholder review and distribution."
}

# Execute main function
main "$@"
