# GEMINI ASM System v2.0
## Global External Monitoring & Intelligence Network Infrastructure

---

## 🚀 SYSTEM OVERVIEW

The GEMINI ASM System is a comprehensive Attack Surface Management solution designed for authorized security testing and red team exercises. This refactored system consolidates and optimizes the original four ASM files into a streamlined, efficient framework.

### **Key Improvements in v2.0:**
- ✅ **Unified Master Prompt** - Single `GEMINI.md` orchestrates entire system
- ✅ **Streamlined File Structure** - Organized hierarchy with clear relationships
- ✅ **Enhanced Automation** - Improved scripts with better error handling
- ✅ **Comprehensive Reporting** - Multi-format reports for different stakeholders
- ✅ **Continuous Monitoring** - Automated surveillance capabilities
- ✅ **Compliance Integration** - Built-in regulatory framework mapping

---

## 📁 FILE STRUCTURE

### **Core System Files**
```
GEMINI_ASM_System/
├── GEMINI.md                           # 🎯 MASTER PROMPT - Primary orchestration document
├── asm_implementation_script.sh        # 🔧 Automated assessment execution
├── gemini_asm_monitor.sh              # 📡 Continuous monitoring script
├── gemini_report_generator.sh         # 📊 Comprehensive report generation
├── asm_monitoring_config.yaml         # ⚙️ Monitoring configuration
├── asm_risk_assessment_template.md    # 📋 Risk assessment template
└── README_GEMINI_ASM.md              # 📖 This documentation
```

### **Generated Assessment Structure**
```
GEMINI_ASM_Assessment_YYYYMMDD_HHMM/
├── 00_authorization_and_scope.txt     # Legal authorization record
├── 01_domain_discovery.txt            # Phase 1: Asset discovery results
├── 02_network_discovery.txt           # Phase 1: Network infrastructure
├── 03_webapp_discovery.txt            # Phase 2: Web application analysis
├── 04_api_discovery.txt               # Phase 2: API discovery results
├── 05_vulnerability_scan.txt          # Phase 3: Vulnerability assessment
├── 06_configuration_analysis.txt      # Phase 3: Configuration security
├── 07_threat_intelligence.txt         # Phase 4: Threat intelligence
├── 08_risk_analysis.txt               # Phase 4: Risk scoring
├── 09_monitoring_setup.txt            # Phase 5: Monitoring framework
├── 10_executive_report.txt            # Phase 6: Executive summary
├── 11_technical_report.txt            # Phase 6: Technical details
├── raw_data/                          # Tool outputs and scan data
├── evidence/                          # Screenshots and proof-of-concept
└── configs/                           # Configuration files
```

---

## 🎯 QUICK START GUIDE

### **1. Authorization Validation**
**CRITICAL:** Ensure you have explicit written authorization before proceeding.

### **2. Basic Assessment Execution**
```bash
# Make scripts executable
chmod +x asm_implementation_script.sh
chmod +x gemini_asm_monitor.sh
chmod +x gemini_report_generator.sh

# Execute comprehensive assessment
./asm_implementation_script.sh target-domain.com
```

### **3. Continuous Monitoring Setup**
```bash
# Configure monitoring (edit asm_monitoring_config.yaml first)
./gemini_asm_monitor.sh asm_monitoring_config.yaml
```

### **4. Report Generation**
```bash
# Generate comprehensive reports
./gemini_report_generator.sh GEMINI_ASM_Assessment_YYYYMMDD_HHMM
```

---

## 📋 GEMINI 6-PHASE METHODOLOGY

### **Phase 1: Reconnaissance & Asset Discovery**
- **Objective:** Comprehensive external attack surface mapping
- **Tools:** subfinder, amass, certificate transparency, DNS analysis
- **Output:** Complete asset inventory with IP mappings and DNS records

### **Phase 2: Web Application & API Analysis**
- **Objective:** Deep analysis of web-facing applications and services
- **Tools:** whatweb, wappalyzer, gobuster, API discovery tools
- **Output:** Technology stack analysis and API endpoint documentation

### **Phase 3: Vulnerability Assessment & Exploitation Analysis**
- **Objective:** Systematic security vulnerability identification
- **Tools:** nikto, nuclei, nessus, testssl.sh, configuration analyzers
- **Output:** Risk-classified vulnerability inventory with CVSS scores

### **Phase 4: Threat Intelligence & Risk Correlation**
- **Objective:** Contextualize findings within threat landscape
- **Analysis:** CVE correlation, threat actor assessment, business impact
- **Output:** Prioritized risk analysis with business context

### **Phase 5: Continuous Monitoring Framework**
- **Objective:** Establish ongoing attack surface surveillance
- **Capabilities:** Automated discovery, change detection, alerting
- **Output:** Monitoring infrastructure and baseline configurations

### **Phase 6: Strategic Reporting & Remediation**
- **Objective:** Deliver actionable intelligence and guidance
- **Deliverables:** Executive summaries, technical reports, compliance mapping
- **Output:** Multi-format reports for different stakeholder audiences

---

## 🛠️ SYSTEM COMPONENTS

### **GEMINI.md - Master Orchestration Prompt**
The primary document that serves as the comprehensive ASM prompt. Contains:
- Complete methodology and phase descriptions
- Legal and ethical framework requirements
- Tool specifications and implementation guidance
- File structure organization and references
- Success metrics and quality assurance standards

### **asm_implementation_script.sh - Automated Execution**
Enhanced automation script with:
- Comprehensive authorization validation
- Structured output organization
- Multi-tool integration and error handling
- Progress tracking and status reporting
- Evidence preservation and chain of custody

### **gemini_asm_monitor.sh - Continuous Monitoring**
Ongoing surveillance capabilities including:
- Automated subdomain discovery and tracking
- Port and service change detection
- Certificate expiration monitoring
- Vulnerability feed integration
- Alert generation and notification

### **gemini_report_generator.sh - Report Generation**
Multi-format reporting system providing:
- Executive summary reports for leadership
- Technical reports for security teams
- Compliance reports for audit requirements
- Risk analysis with business impact assessment
- Remediation roadmaps with timelines

### **asm_monitoring_config.yaml - Configuration Management**
Centralized configuration for:
- Target organization details and scope
- Monitoring intervals and thresholds
- Tool configurations and parameters
- Integration settings (SIEM, ticketing, notifications)
- Compliance framework mappings

### **asm_risk_assessment_template.md - Structured Assessment**
Comprehensive template providing:
- Standardized risk assessment format
- CVSS-based vulnerability scoring
- Business impact analysis framework
- Compliance and regulatory mapping
- Remediation planning and tracking

---

## 🔒 SECURITY & COMPLIANCE

### **Authorization Requirements**
- [ ] **Written Authorization** from target organization
- [ ] **Scope Definition** clearly documented
- [ ] **Legal Boundaries** established and communicated
- [ ] **Qualified Personnel** conducting assessments
- [ ] **Responsible Disclosure** protocols active

### **Compliance Framework Support**
- **NIST Cybersecurity Framework** - Complete lifecycle coverage
- **ISO 27001** - Information security management alignment
- **PCI DSS** - Payment card industry requirements
- **GDPR** - Data protection and privacy considerations
- **SOX** - Financial reporting and internal controls

### **Audit Trail Maintenance**
- **Activity Logging** - Comprehensive assessment audit trail
- **Evidence Preservation** - Secure storage of findings and materials
- **Chain of Custody** - Documented handling procedures
- **Retention Policies** - Compliance with data retention requirements

---

## 📊 SUCCESS METRICS

### **Coverage Metrics**
- Asset Discovery Completeness: >95% of known infrastructure
- Vulnerability Detection Accuracy: <5% false positive rate
- Risk Assessment Precision: Business-aligned impact scoring
- Remediation Tracking: Time-to-fix monitoring and validation

### **Quality Standards**
- **Multi-source Validation** - Cross-reference findings across tools
- **Manual Verification** - Human analysis of critical findings
- **Peer Review Process** - Technical validation by security experts
- **Evidence Documentation** - Comprehensive proof-of-concept materials

---

## 🔄 CONTINUOUS IMPROVEMENT

### **Feedback Integration**
1. **Post-Assessment Review** - Stakeholder feedback collection
2. **Methodology Refinement** - Process optimization based on results
3. **Tool Evaluation** - Regular assessment of tool effectiveness
4. **Threat Landscape Updates** - Integration of emerging threats

### **Knowledge Management**
- **Lessons Learned Database** - Capture insights from assessments
- **Best Practices Repository** - Document effective techniques
- **Threat Intelligence Archive** - Historical threat data analysis
- **Remediation Playbooks** - Standardized response procedures

---

## 🚨 IMPORTANT REMINDERS

### **Legal and Ethical Compliance**
- **AUTHORIZED USE ONLY** - Explicit written permission required
- **SCOPE LIMITATIONS** - Stay within defined assessment boundaries
- **RESPONSIBLE DISCLOSURE** - Follow proper vulnerability reporting
- **PROFESSIONAL STANDARDS** - Maintain highest ethical standards

### **Emergency Procedures**
- **Critical Findings** - Immediate notification to security teams
- **Incident Response** - Follow established response procedures
- **Legal Issues** - Contact legal counsel for any concerns
- **System Impact** - Minimize disruption to target systems

---

## 📞 SUPPORT & CONTACT

For questions, issues, or enhancements to the GEMINI ASM System:

- **System Documentation** - Refer to GEMINI.md for complete methodology
- **Technical Issues** - Review script logs and error messages
- **Compliance Questions** - Consult legal and compliance teams
- **Enhancement Requests** - Document and submit through proper channels

---

**🔐 FINAL AUTHORIZATION REMINDER**: This GEMINI ASM system must only be deployed for authorized security testing and red team exercises. All activities must comply with applicable laws, regulations, and organizational policies. Unauthorized use may result in legal consequences and ethical violations.

---

*GEMINI ASM System v2.0 - Global External Monitoring & Intelligence Network Infrastructure*  
*Developed for authorized security professionals and red team operations*
