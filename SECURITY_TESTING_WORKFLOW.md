# Security Testing Workflow: Tool Execution and Analysis

## Overview
This guide provides instructions for executing security tools in authorized environments, saving outputs to text files, and performing systematic analysis of results.

**⚠️ IMPORTANT**: Only use these techniques on systems you own or have explicit written permission to test.

## Environment Setup

### 1. Create Working Directory Structure
```bash
# Create organized directory structure
mkdir -p security_assessment/{recon,scanning,enumeration,analysis,reports}
cd security_assessment

# Set target variable (replace with authorized target)
export TARGET="authorized-target.com"
export DATE=$(date +%Y%m%d_%H%M%S)
```

### 2. Tool Installation Verification
```bash
# Verify essential tools are installed
echo "=== Tool Verification ===" > tool_check_${DATE}.txt
which nmap >> tool_check_${DATE}.txt
which masscan >> tool_check_${DATE}.txt
which gobuster >> tool_check_${DATE}.txt
which nikto >> tool_check_${DATE}.txt
which sqlmap >> tool_check_${DATE}.txt
```

## Phase 1: Reconnaissance and Information Gathering

### Passive Information Gathering
```bash
# DNS enumeration
echo "=== DNS Enumeration ===" > recon/dns_${TARGET}_${DATE}.txt
nslookup $TARGET >> recon/dns_${TARGET}_${DATE}.txt
dig $TARGET ANY >> recon/dns_${TARGET}_${DATE}.txt
dig @******* $TARGET >> recon/dns_${TARGET}_${DATE}.txt

# Subdomain enumeration (if tools available)
echo "=== Subdomain Discovery ===" > recon/subdomains_${TARGET}_${DATE}.txt
# subfinder -d $TARGET -o recon/subdomains_raw_${TARGET}_${DATE}.txt
# amass enum -d $TARGET >> recon/subdomains_${TARGET}_${DATE}.txt

# WHOIS information
echo "=== WHOIS Information ===" > recon/whois_${TARGET}_${DATE}.txt
whois $TARGET >> recon/whois_${TARGET}_${DATE}.txt
```

### Active Reconnaissance
```bash
# Basic host discovery
echo "=== Host Discovery ===" > recon/hosts_${TARGET}_${DATE}.txt
ping -c 4 $TARGET >> recon/hosts_${TARGET}_${DATE}.txt

# Traceroute analysis
echo "=== Network Path Analysis ===" > recon/traceroute_${TARGET}_${DATE}.txt
traceroute $TARGET >> recon/traceroute_${TARGET}_${DATE}.txt
```

## Phase 2: Network Scanning and Port Discovery

### Port Scanning
```bash
# Quick TCP scan
echo "=== Quick TCP Scan ===" > scanning/tcp_quick_${TARGET}_${DATE}.txt
nmap -sS -T4 --top-ports 1000 $TARGET >> scanning/tcp_quick_${TARGET}_${DATE}.txt

# Comprehensive TCP scan
echo "=== Comprehensive TCP Scan ===" > scanning/tcp_full_${TARGET}_${DATE}.txt
nmap -sS -sV -O -A -p- $TARGET >> scanning/tcp_full_${TARGET}_${DATE}.txt

# UDP scan (top ports)
echo "=== UDP Scan ===" > scanning/udp_${TARGET}_${DATE}.txt
nmap -sU --top-ports 100 $TARGET >> scanning/udp_${TARGET}_${DATE}.txt

# Vulnerability scanning
echo "=== Vulnerability Scan ===" > scanning/vulns_${TARGET}_${DATE}.txt
nmap --script vuln $TARGET >> scanning/vulns_${TARGET}_${DATE}.txt
```

### Service Enumeration
```bash
# HTTP/HTTPS service detection
echo "=== HTTP Service Analysis ===" > enumeration/http_${TARGET}_${DATE}.txt
curl -I http://$TARGET >> enumeration/http_${TARGET}_${DATE}.txt
curl -I https://$TARGET >> enumeration/http_${TARGET}_${DATE}.txt

# Web technology detection
echo "=== Web Technology Detection ===" > enumeration/webtech_${TARGET}_${DATE}.txt
# whatweb $TARGET >> enumeration/webtech_${TARGET}_${DATE}.txt

# Directory/file discovery
echo "=== Directory Discovery ===" > enumeration/dirs_${TARGET}_${DATE}.txt
# gobuster dir -u http://$TARGET -w /usr/share/wordlists/dirb/common.txt >> enumeration/dirs_${TARGET}_${DATE}.txt
```

## Phase 3: Web Application Testing

### Basic Web Application Analysis
```bash
# Nikto web vulnerability scan
echo "=== Nikto Web Scan ===" > enumeration/nikto_${TARGET}_${DATE}.txt
# nikto -h http://$TARGET >> enumeration/nikto_${TARGET}_${DATE}.txt

# SSL/TLS analysis
echo "=== SSL/TLS Analysis ===" > enumeration/ssl_${TARGET}_${DATE}.txt
# sslscan $TARGET >> enumeration/ssl_${TARGET}_${DATE}.txt
openssl s_client -connect $TARGET:443 -servername $TARGET >> enumeration/ssl_${TARGET}_${DATE}.txt

# HTTP headers analysis
echo "=== HTTP Headers Analysis ===" > enumeration/headers_${TARGET}_${DATE}.txt
curl -s -D - http://$TARGET -o /dev/null >> enumeration/headers_${TARGET}_${DATE}.txt
curl -s -D - https://$TARGET -o /dev/null >> enumeration/headers_${TARGET}_${DATE}.txt
```

### Database Testing (if applicable)
```bash
# SQL injection testing (manual verification required)
echo "=== SQL Injection Testing Notes ===" > enumeration/sqli_${TARGET}_${DATE}.txt
echo "Manual testing required - automated tools need careful configuration" >> enumeration/sqli_${TARGET}_${DATE}.txt
# sqlmap -u "http://$TARGET/vulnerable_page?param=value" --batch >> enumeration/sqli_${TARGET}_${DATE}.txt
```

## Phase 4: Analysis and Reporting

### Automated Analysis Script
```bash
#!/bin/bash
# analysis_script.sh

echo "=== SECURITY ASSESSMENT ANALYSIS REPORT ===" > analysis/summary_${TARGET}_${DATE}.txt
echo "Target: $TARGET" >> analysis/summary_${TARGET}_${DATE}.txt
echo "Date: $(date)" >> analysis/summary_${TARGET}_${DATE}.txt
echo "=========================================" >> analysis/summary_${TARGET}_${DATE}.txt

# Analyze open ports
echo "" >> analysis/summary_${TARGET}_${DATE}.txt
echo "=== OPEN PORTS SUMMARY ===" >> analysis/summary_${TARGET}_${DATE}.txt
grep -E "^[0-9]+/(tcp|udp).*open" scanning/*_${TARGET}_${DATE}.txt >> analysis/summary_${TARGET}_${DATE}.txt

# Extract service versions
echo "" >> analysis/summary_${TARGET}_${DATE}.txt
echo "=== SERVICE VERSIONS ===" >> analysis/summary_${TARGET}_${DATE}.txt
grep -E "VERSION" scanning/*_${TARGET}_${DATE}.txt >> analysis/summary_${TARGET}_${DATE}.txt

# Identify potential vulnerabilities
echo "" >> analysis/summary_${TARGET}_${DATE}.txt
echo "=== POTENTIAL VULNERABILITIES ===" >> analysis/summary_${TARGET}_${DATE}.txt
grep -i -E "(vulnerable|exploit|CVE-|critical|high)" scanning/*_${TARGET}_${DATE}.txt >> analysis/summary_${TARGET}_${DATE}.txt

# Web application findings
echo "" >> analysis/summary_${TARGET}_${DATE}.txt
echo "=== WEB APPLICATION FINDINGS ===" >> analysis/summary_${TARGET}_${DATE}.txt
grep -i -E "(directory|admin|login|sql|xss)" enumeration/*_${TARGET}_${DATE}.txt >> analysis/summary_${TARGET}_${DATE}.txt
```

### Manual Analysis Guidelines
```bash
# Create manual analysis template
cat > analysis/manual_analysis_${TARGET}_${DATE}.txt << EOF
=== MANUAL ANALYSIS CHECKLIST ===

1. NETWORK ANALYSIS:
   [ ] Review port scan results for unusual services
   [ ] Check for default credentials on discovered services
   [ ] Analyze service versions for known vulnerabilities
   
2. WEB APPLICATION ANALYSIS:
   [ ] Review HTTP headers for security misconfigurations
   [ ] Check for directory traversal possibilities
   [ ] Analyze forms for injection vulnerabilities
   [ ] Test authentication mechanisms
   
3. SSL/TLS ANALYSIS:
   [ ] Review certificate validity and configuration
   [ ] Check for weak cipher suites
   [ ] Verify proper certificate chain
   
4. VULNERABILITY PRIORITIZATION:
   [ ] Critical: Remote code execution, SQL injection
   [ ] High: Authentication bypass, privilege escalation
   [ ] Medium: Information disclosure, weak encryption
   [ ] Low: Information leakage, minor misconfigurations

5. RECOMMENDATIONS:
   [ ] Immediate actions required
   [ ] Security improvements
   [ ] Monitoring recommendations
   
=== FINDINGS SUMMARY ===
[Document specific findings here]

=== RISK ASSESSMENT ===
[Provide risk ratings and business impact]

=== REMEDIATION STEPS ===
[Detailed fix recommendations]
EOF
```

## Phase 5: Report Generation

### Comprehensive Report Script
```bash
#!/bin/bash
# generate_report.sh

REPORT_FILE="reports/security_assessment_${TARGET}_${DATE}.txt"

cat > $REPORT_FILE << EOF
========================================
SECURITY ASSESSMENT REPORT
========================================
Target: $TARGET
Assessment Date: $(date)
Assessor: [Your Name]
========================================

EXECUTIVE SUMMARY:
[Brief overview of findings and risk level]

METHODOLOGY:
- Reconnaissance and information gathering
- Network scanning and service enumeration
- Web application testing
- Vulnerability assessment
- Manual verification of findings

SCOPE:
- Target: $TARGET
- Testing approach: External black-box assessment
- Tools used: Nmap, Nikto, custom scripts

========================================
EOF

# Append technical findings
echo "" >> $REPORT_FILE
echo "TECHNICAL FINDINGS:" >> $REPORT_FILE
echo "===================" >> $REPORT_FILE
cat analysis/summary_${TARGET}_${DATE}.txt >> $REPORT_FILE

# Append manual analysis
echo "" >> $REPORT_FILE
echo "MANUAL ANALYSIS:" >> $REPORT_FILE
echo "================" >> $REPORT_FILE
cat analysis/manual_analysis_${TARGET}_${DATE}.txt >> $REPORT_FILE

echo "Report generated: $REPORT_FILE"
```

## Usage Instructions

1. **Setup Environment**: Run environment setup commands
2. **Execute Phases**: Run each phase sequentially, saving outputs
3. **Analyze Results**: Use analysis scripts and manual review
4. **Generate Report**: Create comprehensive assessment report
5. **Cleanup**: Securely store or delete sensitive data

## Important Notes

- Always obtain proper authorization before testing
- Follow responsible disclosure practices
- Document all activities thoroughly
- Respect rate limits and avoid service disruption
- Store results securely and delete when no longer needed

## Advanced Analysis Techniques

### Log Parsing and Pattern Recognition
```bash
# Create advanced analysis script
cat > analysis/advanced_parser.sh << 'EOF'
#!/bin/bash

TARGET=$1
DATE=$2

echo "=== ADVANCED ANALYSIS RESULTS ===" > analysis/advanced_${TARGET}_${DATE}.txt

# Parse for interesting ports and services
echo "HIGH-VALUE TARGETS:" >> analysis/advanced_${TARGET}_${DATE}.txt
grep -E "(21|22|23|25|53|80|110|143|443|993|995|1433|3306|3389|5432|5900)" scanning/*_${TARGET}_${DATE}.txt >> analysis/advanced_${TARGET}_${DATE}.txt

# Extract potential attack vectors
echo "" >> analysis/advanced_${TARGET}_${DATE}.txt
echo "POTENTIAL ATTACK VECTORS:" >> analysis/advanced_${TARGET}_${DATE}.txt
grep -i -E "(anonymous|guest|admin|root|default|weak|unencrypted)" */*_${TARGET}_${DATE}.txt >> analysis/advanced_${TARGET}_${DATE}.txt

# Identify information disclosure
echo "" >> analysis/advanced_${TARGET}_${DATE}.txt
echo "INFORMATION DISCLOSURE:" >> analysis/advanced_${TARGET}_${DATE}.txt
grep -i -E "(version|server|powered|generator|framework)" */*_${TARGET}_${DATE}.txt >> analysis/advanced_${TARGET}_${DATE}.txt

# Security header analysis
echo "" >> analysis/advanced_${TARGET}_${DATE}.txt
echo "SECURITY HEADERS ANALYSIS:" >> analysis/advanced_${TARGET}_${DATE}.txt
grep -i -E "(x-frame|content-security|strict-transport|x-content-type)" enumeration/headers_${TARGET}_${DATE}.txt >> analysis/advanced_${TARGET}_${DATE}.txt || echo "No security headers found" >> analysis/advanced_${TARGET}_${DATE}.txt
EOF

chmod +x analysis/advanced_parser.sh
```

### Automated Vulnerability Correlation
```bash
# Create vulnerability correlation script
cat > analysis/vuln_correlator.sh << 'EOF'
#!/bin/bash

TARGET=$1
DATE=$2

echo "=== VULNERABILITY CORRELATION REPORT ===" > analysis/correlation_${TARGET}_${DATE}.txt

# Cross-reference findings
echo "CROSS-REFERENCED VULNERABILITIES:" >> analysis/correlation_${TARGET}_${DATE}.txt

# Check for common vulnerability patterns
if grep -q "Apache" scanning/*_${TARGET}_${DATE}.txt; then
    echo "- Apache server detected - Check for mod_* vulnerabilities" >> analysis/correlation_${TARGET}_${DATE}.txt
fi

if grep -q "nginx" scanning/*_${TARGET}_${DATE}.txt; then
    echo "- Nginx server detected - Check for configuration issues" >> analysis/correlation_${TARGET}_${DATE}.txt
fi

if grep -q "MySQL" scanning/*_${TARGET}_${DATE}.txt; then
    echo "- MySQL detected - Test for SQL injection and weak credentials" >> analysis/correlation_${TARGET}_${DATE}.txt
fi

if grep -q "SSH" scanning/*_${TARGET}_${DATE}.txt; then
    echo "- SSH service detected - Test for weak authentication" >> analysis/correlation_${TARGET}_${DATE}.txt
fi

# Generate risk matrix
echo "" >> analysis/correlation_${TARGET}_${DATE}.txt
echo "RISK MATRIX:" >> analysis/correlation_${TARGET}_${DATE}.txt
echo "Critical: $(grep -c -i 'critical' */*_${TARGET}_${DATE}.txt) findings" >> analysis/correlation_${TARGET}_${DATE}.txt
echo "High: $(grep -c -i 'high' */*_${TARGET}_${DATE}.txt) findings" >> analysis/correlation_${TARGET}_${DATE}.txt
echo "Medium: $(grep -c -i 'medium' */*_${TARGET}_${DATE}.txt) findings" >> analysis/correlation_${TARGET}_${DATE}.txt
echo "Low: $(grep -c -i 'low' */*_${TARGET}_${DATE}.txt) findings" >> analysis/correlation_${TARGET}_${DATE}.txt
EOF

chmod +x analysis/vuln_correlator.sh
```

### Master Execution Script
```bash
# Create master execution script
cat > run_assessment.sh << 'EOF'
#!/bin/bash

if [ $# -eq 0 ]; then
    echo "Usage: $0 <target>"
    echo "Example: $0 authorized-target.com"
    exit 1
fi

TARGET=$1
DATE=$(date +%Y%m%d_%H%M%S)

echo "Starting security assessment of $TARGET at $(date)"
echo "Results will be saved with timestamp: $DATE"

# Create directory structure
mkdir -p security_assessment/{recon,scanning,enumeration,analysis,reports}
cd security_assessment

# Phase 1: Reconnaissance
echo "[+] Phase 1: Reconnaissance"
./run_recon.sh $TARGET $DATE

# Phase 2: Scanning
echo "[+] Phase 2: Network Scanning"
./run_scanning.sh $TARGET $DATE

# Phase 3: Enumeration
echo "[+] Phase 3: Service Enumeration"
./run_enumeration.sh $TARGET $DATE

# Phase 4: Analysis
echo "[+] Phase 4: Analysis"
./analysis/advanced_parser.sh $TARGET $DATE
./analysis/vuln_correlator.sh $TARGET $DATE

# Phase 5: Report Generation
echo "[+] Phase 5: Report Generation"
./generate_report.sh $TARGET $DATE

echo "Assessment complete. Check reports/ directory for results."
EOF

chmod +x run_assessment.sh
```

### Individual Phase Scripts
```bash
# Reconnaissance script
cat > run_recon.sh << 'EOF'
#!/bin/bash
TARGET=$1
DATE=$2

echo "Starting reconnaissance phase for $TARGET"

# DNS enumeration
nslookup $TARGET > recon/dns_${TARGET}_${DATE}.txt 2>&1
dig $TARGET ANY >> recon/dns_${TARGET}_${DATE}.txt 2>&1

# WHOIS lookup
whois $TARGET > recon/whois_${TARGET}_${DATE}.txt 2>&1

# Basic connectivity
ping -c 4 $TARGET > recon/ping_${TARGET}_${DATE}.txt 2>&1
traceroute $TARGET > recon/traceroute_${TARGET}_${DATE}.txt 2>&1

echo "Reconnaissance phase completed"
EOF

chmod +x run_recon.sh

# Scanning script
cat > run_scanning.sh << 'EOF'
#!/bin/bash
TARGET=$1
DATE=$2

echo "Starting scanning phase for $TARGET"

# Port scans
nmap -sS -T4 --top-ports 1000 $TARGET > scanning/tcp_quick_${TARGET}_${DATE}.txt 2>&1
nmap -sS -sV -O -A $TARGET > scanning/tcp_detailed_${TARGET}_${DATE}.txt 2>&1
nmap --script vuln $TARGET > scanning/vulns_${TARGET}_${DATE}.txt 2>&1

echo "Scanning phase completed"
EOF

chmod +x run_scanning.sh

# Enumeration script
cat > run_enumeration.sh << 'EOF'
#!/bin/bash
TARGET=$1
DATE=$2

echo "Starting enumeration phase for $TARGET"

# HTTP analysis
curl -I http://$TARGET > enumeration/http_${TARGET}_${DATE}.txt 2>&1
curl -I https://$TARGET >> enumeration/http_${TARGET}_${DATE}.txt 2>&1

# Headers analysis
curl -s -D - http://$TARGET -o /dev/null > enumeration/headers_${TARGET}_${DATE}.txt 2>&1
curl -s -D - https://$TARGET -o /dev/null >> enumeration/headers_${TARGET}_${DATE}.txt 2>&1

# SSL analysis
echo | openssl s_client -connect $TARGET:443 -servername $TARGET > enumeration/ssl_${TARGET}_${DATE}.txt 2>&1

echo "Enumeration phase completed"
EOF

chmod +x run_enumeration.sh
```

## Legal Disclaimer

This workflow is intended for authorized security testing only. Unauthorized use against systems you do not own or lack permission to test is illegal and unethical.

## Quick Start Guide

1. **Make scripts executable**: `chmod +x *.sh`
2. **Run assessment**: `./run_assessment.sh your-authorized-target.com`
3. **Review results**: Check `reports/` directory
4. **Analyze findings**: Review all generated text files
5. **Create action plan**: Use manual analysis template
