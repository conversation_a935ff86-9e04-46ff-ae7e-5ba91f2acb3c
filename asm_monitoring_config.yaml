# Attack Surface Management - Continuous Monitoring Configuration
# This configuration defines monitoring parameters for ongoing ASM activities

monitoring_config:
  # Target organization details
  organization:
    name: "Target Organization"
    primary_domain: "example.com"
    additional_domains:
      - "example.org"
      - "example.net"
    ip_ranges:
      - "***********/24"
      - "10.0.0.0/16"
    
  # Monitoring intervals (in hours)
  scan_intervals:
    subdomain_discovery: 24      # Daily subdomain enumeration
    port_scanning: 168          # Weekly port scans
    vulnerability_assessment: 72 # Every 3 days
    certificate_monitoring: 24   # Daily certificate checks
    dns_monitoring: 12          # Every 12 hours
    
  # Alert thresholds
  alerting:
    new_subdomains: true        # Alert on new subdomain discovery
    new_open_ports: true        # Alert on new open ports
    certificate_expiry_days: 30 # Alert when certificates expire within 30 days
    vulnerability_severity: "HIGH" # Alert on HIGH and CRITICAL vulnerabilities
    service_changes: true       # Alert on service version changes
    
  # Notification settings
  notifications:
    email:
      enabled: true
      smtp_server: "smtp.company.com"
      smtp_port: 587
      recipients:
        - "<EMAIL>"
        - "<EMAIL>"
    
    slack:
      enabled: false
      webhook_url: "https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK"
      channel: "#security-alerts"
    
    syslog:
      enabled: true
      server: "siem.company.com"
      port: 514
      facility: "local0"

# Tool configurations
tools:
  # Subdomain enumeration tools
  subdomain_discovery:
    subfinder:
      enabled: true
      config_file: "/opt/subfinder/config.yaml"
      sources: ["all"]
      timeout: 30
      
    amass:
      enabled: true
      config_file: "/opt/amass/config.ini"
      mode: "passive"
      timeout: 60
      
    certificate_transparency:
      enabled: true
      sources:
        - "crt.sh"
        - "censys"
        - "facebook"
      
  # Port scanning configuration
  port_scanning:
    nmap:
      enabled: true
      scan_type: "sS"  # SYN scan
      timing: "T4"     # Aggressive timing
      ports: "1-65535" # Full port range
      scripts: "default,vuln"
      max_rate: 1000
      
  # Web application scanning
  web_scanning:
    whatweb:
      enabled: true
      aggression: 3
      plugins: "all"
      
    nikto:
      enabled: true
      timeout: 600
      max_scan_time: 3600
      
    gobuster:
      enabled: true
      wordlist: "/usr/share/wordlists/dirb/common.txt"
      threads: 10
      timeout: 10
      
  # SSL/TLS analysis
  ssl_analysis:
    testssl:
      enabled: true
      protocols: "all"
      vulnerabilities: true
      cipher_tests: true
      
    sslscan:
      enabled: true
      show_certificate: true
      check_ciphers: true

# Data storage and retention
data_management:
  storage:
    base_directory: "/var/lib/asm"
    results_retention_days: 90
    logs_retention_days: 365
    
  database:
    type: "postgresql"
    host: "localhost"
    port: 5432
    database: "asm_db"
    username: "asm_user"
    password_file: "/etc/asm/db_password"
    
  backup:
    enabled: true
    frequency: "daily"
    retention_days: 30
    destination: "/backup/asm"

# Risk scoring configuration
risk_scoring:
  # CVSS base score multipliers
  cvss_multipliers:
    network_exposure: 1.5    # Multiply score for internet-facing assets
    critical_service: 1.3    # Multiply for critical business services
    no_authentication: 1.2   # Multiply for unauthenticated services
    
  # Asset criticality levels
  asset_criticality:
    critical:
      - "*.company.com"
      - "api.company.com"
      - "admin.company.com"
    high:
      - "*.prod.company.com"
      - "mail.company.com"
    medium:
      - "*.staging.company.com"
      - "test.company.com"
    low:
      - "*.dev.company.com"
      - "sandbox.company.com"

# Compliance and reporting
compliance:
  frameworks:
    - "NIST_CSF"
    - "ISO_27001"
    - "PCI_DSS"
    - "SOX"
    
  reporting:
    executive_summary: true
    technical_details: true
    compliance_mapping: true
    trend_analysis: true
    
  export_formats:
    - "PDF"
    - "JSON"
    - "CSV"
    - "XML"

# Integration settings
integrations:
  # SIEM integration
  siem:
    enabled: true
    type: "splunk"
    endpoint: "https://splunk.company.com:8088"
    token_file: "/etc/asm/splunk_token"
    
  # Vulnerability management
  vulnerability_management:
    enabled: true
    type: "tenable"
    api_endpoint: "https://cloud.tenable.com"
    access_key_file: "/etc/asm/tenable_keys"
    
  # Ticketing system
  ticketing:
    enabled: true
    type: "jira"
    server: "https://company.atlassian.net"
    project: "SEC"
    credentials_file: "/etc/asm/jira_creds"

# Security and access control
security:
  # API authentication
  api_authentication:
    method: "token"
    token_file: "/etc/asm/api_token"
    token_rotation_days: 90
    
  # Access control
  access_control:
    admin_users:
      - "security-admin"
      - "asm-operator"
    read_only_users:
      - "security-analyst"
      - "compliance-officer"
      
  # Audit logging
  audit_logging:
    enabled: true
    log_file: "/var/log/asm/audit.log"
    log_level: "INFO"
    include_api_calls: true

# Performance and resource limits
performance:
  # Concurrent scan limits
  concurrency:
    max_subdomain_scans: 10
    max_port_scans: 5
    max_web_scans: 3
    
  # Resource limits
  resources:
    max_memory_mb: 4096
    max_cpu_percent: 80
    max_disk_usage_gb: 100
    
  # Rate limiting
  rate_limiting:
    requests_per_second: 10
    burst_limit: 50
    backoff_strategy: "exponential"

# Logging configuration
logging:
  level: "INFO"
  format: "json"
  output:
    - type: "file"
      path: "/var/log/asm/application.log"
      rotation: "daily"
      retention_days: 30
    - type: "syslog"
      facility: "local1"
      tag: "asm"

# Health monitoring
health_monitoring:
  enabled: true
  check_interval: 300  # 5 minutes
  endpoints:
    - name: "database"
      type: "postgresql"
      critical: true
    - name: "storage"
      type: "filesystem"
      path: "/var/lib/asm"
      critical: true
    - name: "external_apis"
      type: "http"
      urls:
        - "https://crt.sh"
        - "https://api.shodan.io"
      critical: false

# Maintenance and updates
maintenance:
  # Automatic updates
  auto_updates:
    enabled: true
    schedule: "0 2 * * 0"  # Weekly on Sunday at 2 AM
    include_tools: true
    include_wordlists: true
    
  # Database maintenance
  database_maintenance:
    vacuum_schedule: "0 3 * * 0"  # Weekly vacuum
    reindex_schedule: "0 4 1 * *"  # Monthly reindex
    
  # Cleanup tasks
  cleanup:
    temp_files: true
    old_scans: true
    orphaned_data: true
    schedule: "0 1 * * *"  # Daily at 1 AM
